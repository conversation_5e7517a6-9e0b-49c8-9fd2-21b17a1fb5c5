# Funcionalidad de Invitación a Socios

## Descripción
Esta funcionalidad permite a los owners enviar invitaciones por email a potenciales socios para unirse a su red profesional inmobiliaria.

## Archivos Implementados

### 1. Template de Email
- **Archivo**: `templates/invitacion-socio.html`
- **Descripción**: Template HTML profesional para el email de invitación
- **Características**:
  - Diseño responsive
  - Branding de Multibolsa Inmobiliaria
  - Mensaje personalizable
  - Lista de beneficios
  - Botón de aceptación
  - Información de contacto

### 2. Servicio de Email
- **Archivo**: `src/Application/Services/EmailService.php`
- **Descripción**: Servicio para envío de emails via Brevo API
- **Métodos principales**:
  - `enviarInvitacionSocio()`: Envía email de invitación
  - `isConfigured()`: Valida configuración de Brevo
  - `generateTextContent()`: Genera versión texto del email

### 3. Acción Principal
- **Archivo**: `src/Application/Actions/Owner/Socios/InvitarActivacionSocioOwnerAction.php`
- **Descripción**: Controlador que procesa la invitación
- **Funcionalidades**:
  - Validación de datos de entrada
  - Obtención de información del owner
  - Preparación de datos para template
  - Envío del email
  - Logging de eventos

### 4. Configuración
- **Archivos**: `app/settings.php`, `app/dependencies.php`
- **Descripción**: Configuración de Brevo y registro del servicio

## Datos de Entrada (Frontend)

El endpoint espera recibir los siguientes datos via POST:

```json
{
  "email": "<EMAIL>",
  "nombre": "Valente",
  "telefono": "+52 77 7442 9429",
  "mensaje": "Hola Valente,\n\nEspero te encuentres bien..."
}
```

## Variables de Entorno Requeridas

```bash
# Configuración de Brevo
BREVO_API_KEY=your_brevo_api_key_here
BREVO_FROM_EMAIL=<EMAIL>
BREVO_FROM_NAME=Multibolsa Inmobiliaria

# Dominio de la aplicación (para enlaces)
APP_DOMAIN=https://multibolsa.com
```

## Respuesta del API

### Éxito
```json
{
  "statusCode": 200,
  "data": {
    "success": true,
    "message": "Invitación enviada exitosamente"
  }
}
```

### Error de validación
```json
{
  "statusCode": 400,
  "error": {
    "type": "BAD_REQUEST",
    "description": "El campo 'email' es requerido"
  }
}
```

### Error interno
```json
{
  "statusCode": 500,
  "data": {
    "success": false,
    "message": "Error al enviar la invitación. Por favor, inténtalo de nuevo."
  }
}
```

## Configuración de Brevo

1. Crear cuenta en [Brevo](https://www.brevo.com/)
2. Obtener API Key desde el panel de administración
3. Configurar las variables de entorno
4. Verificar el dominio de envío (opcional pero recomendado)

## Logging

El sistema registra los siguientes eventos:

- **Info**: Invitación enviada exitosamente
- **Error**: Errores de configuración, envío o validación
- **Debug**: Detalles de la comunicación con Brevo API

Los logs se almacenan en `logs/app.log`.

## Funcionalidades Futuras

1. **Sistema de tokens únicos**: Para enlaces de aceptación personalizados
2. **Seguimiento de invitaciones**: Base de datos para tracking
3. **Templates personalizables**: Permitir a owners personalizar templates
4. **Recordatorios automáticos**: Reenvío automático de invitaciones
5. **Analytics**: Métricas de apertura y clics

## Testing

Para probar la funcionalidad:

1. Configurar las variables de entorno de Brevo
2. Hacer POST a `/owner/socios/invitar` con los datos requeridos
3. Verificar logs en `logs/app.log`
4. Revisar email recibido

## Troubleshooting

### Error: "Servicio de email no disponible"
- Verificar que `BREVO_API_KEY` esté configurada
- Verificar que el servicio esté registrado en `app/dependencies.php`

### Error: "No se pudo obtener información del remitente"
- Verificar que el token de autenticación sea válido
- Verificar que el contrato exista en la base de datos

### Email no se envía
- Verificar API Key de Brevo
- Revisar logs para detalles del error
- Verificar conectividad a internet
