<?php

declare(strict_types=1);

namespace App\Application\Services;

use App\Application\Settings\SettingsInterface;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;
use Mustache_Engine;
use Psr\Log\LoggerInterface;

class EmailService
{
    private array $brevoConfig;
    private Client $httpClient;
    private LoggerInterface $logger;
    private Mustache_Engine $mustache;

    public function __construct(
        SettingsInterface $settings,
        Client $httpClient,
        LoggerInterface $logger
    ) {
        $this->brevoConfig = $settings->get('brevo');
        $this->httpClient = $httpClient;
        $this->logger = $logger;
        $this->mustache = new Mustache_Engine([
            'loader' => new \Mustache_Loader_FilesystemLoader(__DIR__ . '/../../../templates')
        ]);
    }

    /**
     * Envía un email de invitación a socio usando Brevo
     *
     * @param string $toEmail Email del destinatario
     * @param string $toName Nombre del destinatario
     * @param array $templateData Datos para el template
     * @return bool True si se envió correctamente, false en caso contrario
     */
    public function enviarInvitacionSocio(string $toEmail, string $toName, array $templateData): bool
    {
        try {
            // Cargar y procesar el template
            $htmlContent = $this->mustache->render('invitacion-socio.html', $templateData);

            // Preparar los datos para Brevo
            $emailData = [
                'sender' => [
                    'name' => $this->brevoConfig['from_name'],
                    'email' => $this->brevoConfig['from_email']
                ],
                'to' => [
                    [
                        'email' => $toEmail,
                        'name' => $toName
                    ]
                ],
                'subject' => 'Invitación a Red de Socios Inmobiliarios - ' . ($templateData['nombre_remitente'] ?? 'Multibolsa'),
                'htmlContent' => $htmlContent,
                'textContent' => $this->generateTextContent($templateData)
            ];

            // Enviar el email via Brevo API
            $response = $this->httpClient->post($this->brevoConfig['api_url'] . '/smtp/email', [
                'headers' => [
                    'Accept' => 'application/json',
                    'Content-Type' => 'application/json',
                    'api-key' => $this->brevoConfig['api_key']
                ],
                'json' => $emailData
            ]);

            $statusCode = $response->getStatusCode();
            $responseBody = $response->getBody()->getContents();

            if ($statusCode === 201) {
                $this->logger->info('Email de invitación enviado exitosamente', [
                    'to_email' => $toEmail,
                    'to_name' => $toName,
                    'response' => $responseBody
                ]);
                return true;
            } else {
                $this->logger->error('Error al enviar email de invitación', [
                    'status_code' => $statusCode,
                    'response' => $responseBody,
                    'to_email' => $toEmail
                ]);
                return false;
            }

        } catch (GuzzleException $e) {
            $this->logger->error('Error de conexión al enviar email via Brevo', [
                'error' => $e->getMessage(),
                'to_email' => $toEmail,
                'to_name' => $toName
            ]);
            return false;
        } catch (\Exception $e) {
            $this->logger->error('Error general al enviar email de invitación', [
                'error' => $e->getMessage(),
                'to_email' => $toEmail,
                'to_name' => $toName
            ]);
            return false;
        }
    }

    /**
     * Genera contenido de texto plano para el email
     *
     * @param array $templateData
     * @return string
     */
    private function generateTextContent(array $templateData): string
    {
        $textContent = "Hola {$templateData['nombre']},\n\n";
        $textContent .= strip_tags($templateData['mensaje']) . "\n\n";
        $textContent .= "Beneficios de unirte a nuestra red:\n";
        $textContent .= "• Acceso a oportunidades exclusivas de negocio\n";
        $textContent .= "• Expansión de tu cartera de clientes y propiedades\n";
        $textContent .= "• Creación de tu propia Multibolsa Inmobiliaria\n";
        $textContent .= "• Conexión con otros profesionales del sector\n";
        $textContent .= "• Herramientas profesionales para potenciar tu negocio\n\n";
        
        if (isset($templateData['enlace_aceptacion'])) {
            $textContent .= "Para aceptar la invitación, visita: {$templateData['enlace_aceptacion']}\n\n";
        }
        
        $textContent .= "Información de contacto:\n";
        $textContent .= "Teléfono: {$templateData['telefono']}\n";
        $textContent .= "Email: {$templateData['email_remitente']}\n\n";
        $textContent .= "¡Espero que podamos colaborar pronto!\n\n";
        $textContent .= "Saludos cordiales,\n";
        $textContent .= $templateData['nombre_remitente'] . "\n";
        
        if (isset($templateData['empresa_remitente'])) {
            $textContent .= $templateData['empresa_remitente'] . "\n";
        }
        
        $textContent .= "\n---\n";
        $textContent .= "Este email fue enviado desde la plataforma Multibolsa Inmobiliaria\n";

        return $textContent;
    }

    /**
     * Valida la configuración de Brevo
     *
     * @return bool
     */
    public function isConfigured(): bool
    {
        return !empty($this->brevoConfig['api_key']) && 
               !empty($this->brevoConfig['api_url']) &&
               !empty($this->brevoConfig['from_email']);
    }
}
