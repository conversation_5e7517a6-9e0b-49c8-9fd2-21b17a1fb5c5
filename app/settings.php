<?php

declare(strict_types=1);

use App\Application\Settings\Settings;
use App\Application\Settings\SettingsInterface;
use DI\ContainerBuilder;
use Monolog\Logger;

return function (ContainerBuilder $containerBuilder) {
    // Global Settings Object
    $containerBuilder->addDefinitions([
        SettingsInterface::class => function () {
            return new Settings([
                'displayErrorDetails' => true, // Should be set to false in production
                'logError' => false,
                'logErrorDetails' => false,
                'logger' => [
                    'name' => 'msi-inmuebles-preguntas',
                    //                    'path' => isset($_ENV['docker']) ? 'php://stdout' : __DIR__ . '/../logs/app.log',
                    'path' => __DIR__.'/../logs/app.log',
                    'level' => Logger::DEBUG,
                ],

                'sistemainmobiliario' => [
                    'driver' => 'mariadb',
                    'host' => getenv('SI_MYSQL_SERVER'),
                    'database' => getenv('SI_MYSQL_DATABASE'),
                    'username' => getenv('SI_MYSQL_USERNAME'),
                    'password' => getenv('SI_MYSQL_PASSWORD'),
                    'charset' => 'utf8mb4',
                    'collation' => 'utf8mb4_general_ci',
                    'prefix' => '',
                ],
                'publiweb' => [
                    'driver' => 'mariadb',
                    'host' => getenv('PW_MYSQL_SERVER'),
                    'database' => getenv('PW_MYSQL_DATABASE'),
                    'username' => getenv('PW_MYSQL_USERNAME'),
                    'password' => getenv('PW_MYSQL_PASSWORD'),
                    'charset' => 'utf8mb4',
                    'collation' => 'utf8mb4_general_ci',
                    'prefix' => '',
                ],

                'brevo' => [
                    'api_key' => getenv('BREVO_API_KEY'),
                    'api_url' => 'https://api.brevo.com/v3',
                    'from_email' => getenv('BREVO_FROM_EMAIL') ?: '<EMAIL>',
                    'from_name' => getenv('BREVO_FROM_NAME') ?: 'Multibolsa Inmobiliaria',
                ],
            ]);
        }
    ]);
};
