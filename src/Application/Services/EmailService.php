<?php

declare(strict_types=1);

namespace App\Application\Services;

use App\Application\Settings\SettingsInterface;
use Mustache_Engine;
use PHPMailer\PHPMailer\Exception;
use PHPMailer\PHPMailer\PHPMailer;
use Psr\Log\LoggerInterface;

class EmailService
{
    private array $brevoConfig;
    private LoggerInterface $logger;
    private Mustache_Engine $mustache;

    public function __construct(
        SettingsInterface $settings,
        LoggerInterface $logger
    ) {
        $this->brevoConfig = $settings->get('brevo');
        $this->logger = $logger;
        // Configurar Mustache sin loader (cargaremos templates manualmente)
        $this->mustache = new Mustache_Engine();
    }

    /**
     * Envía un email de invitación a socio usando Brevo
     *
     * @param string $toEmail Email del destinatario
     * @param string $toName Nombre del destinatario
     * @param array $templateData Datos para el template
     * @return bool True si se envió correctamente, false en caso contrario
     */
    public function enviarInvitacionSocio(string $toEmail, string $toName, array $templateData): bool
    {
        try {
            $this->logger->debug('Attempting to render template', [
                'template' => 'invitacion-socio.html',
                'to_email' => $toEmail
            ]);

            // Cargar template directamente
            $templatesPath = dirname(__DIR__, 3) . '/templates';
            $templateFile = $templatesPath . '/invitacion-socio.html';

            $this->logger->debug('Template file path', [
                'template_file' => $templateFile,
                'file_exists' => file_exists($templateFile)
            ]);

            if (!file_exists($templateFile)) {
                throw new \Exception("Template file not found: $templateFile");
            }

            $templateContent = file_get_contents($templateFile);
            if ($templateContent === false) {
                throw new \Exception("Could not read template file: $templateFile");
            }

            // Procesar el template con Mustache
            $htmlContent = $this->mustache->render($templateContent, $templateData);

            $this->logger->debug('Template rendered successfully', [
                'content_length' => strlen($htmlContent)
            ]);

            // Crear instancia de PHPMailer
            $mail = new PHPMailer(true);

            // Configuración del servidor SMTP
            $mail->isSMTP();
            $mail->Host = $this->brevoConfig['smtp_server'];
            $mail->SMTPAuth = true;
            $mail->Username = $this->brevoConfig['smtp_username'];
            $mail->Password = $this->brevoConfig['smtp_password'];
            $mail->SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS;
            $mail->Port = $this->brevoConfig['smtp_port'];
            $mail->CharSet = 'UTF-8';

            // Configuración del email
            $mail->setFrom(
                $this->brevoConfig['from_email'],
                $this->brevoConfig['from_name']
            );

            // Reply-To al email del remitente original
            if (!empty($templateData['email_remitente'])) {
                $mail->addReplyTo(
                    $templateData['email_remitente'],
                    $templateData['nombre_remitente'] ?? ''
                );
            }

            $mail->addAddress($toEmail, $toName);

            // Contenido del email
            $mail->isHTML(true);
            $mail->Subject = 'Invitación a Red de Socios Inmobiliarios - ' . ($templateData['nombre_remitente'] ?? 'Multibolsa');
            $mail->Body = $htmlContent;
            $mail->AltBody = $this->generateTextContent($templateData);

            $this->logger->debug('Sending email via Brevo SMTP', [
                'to_email' => $toEmail,
                'from_email' => $this->brevoConfig['from_email'],
                'smtp_server' => $this->brevoConfig['smtp_server'],
                'subject' => $mail->Subject
            ]);

            // Enviar el email
            $result = $mail->send();

//            dd($result);

            if ($result) {
                $this->logger->info('Email de invitación enviado exitosamente via SMTP', [
                    'to_email' => $toEmail,
                    'to_name' => $toName,
                    'smtp_server' => $this->brevoConfig['smtp_server'],
                    'result' => $result
                ]);
                return true;
            } else {
                $this->logger->error('Error al enviar email via SMTP', [
                    'to_email' => $toEmail,
                    'error_info' => $mail->ErrorInfo
                ]);
                return false;
            }
        } catch (Exception $e) {
            $this->logger->error('PHPMailer Exception al enviar email', [
                'error' => $e->getMessage(),
                'to_email' => $toEmail,
                'to_name' => $toName
            ]);
            return false;
        } catch (\Exception $e) {
            $this->logger->error('Error general al enviar email de invitación', [
                'error' => $e->getMessage(),
                'to_email' => $toEmail,
                'to_name' => $toName
            ]);
            return false;
        }
    }

    /**
     * Genera contenido de texto plano para el email
     *
     * @param array $templateData
     * @return string
     */
    private function generateTextContent(array $templateData): string
    {
        $textContent = "Hola {$templateData['nombre']},\n\n";
        $textContent .= strip_tags($templateData['mensaje']) . "\n\n";
        $textContent .= "Beneficios de unirte a nuestra red:\n";
        $textContent .= "• Acceso a oportunidades exclusivas de negocio\n";
        $textContent .= "• Expansión de tu cartera de clientes y propiedades\n";
        $textContent .= "• Creación de tu propia Multibolsa Inmobiliaria\n";
        $textContent .= "• Conexión con otros profesionales del sector\n";
        $textContent .= "• Herramientas profesionales para potenciar tu negocio\n\n";

        if (isset($templateData['enlace_aceptacion'])) {
            $textContent .= "Para aceptar la invitación, visita: {$templateData['enlace_aceptacion']}\n\n";
        }

        $textContent .= "Información de contacto:\n";
        $textContent .= "Teléfono: {$templateData['telefono']}\n";
        $textContent .= "Email: {$templateData['email_remitente']}\n\n";
        $textContent .= "¡Espero que podamos colaborar pronto!\n\n";
        $textContent .= "Saludos cordiales,\n";
        $textContent .= $templateData['nombre_remitente'] . "\n";

        if (isset($templateData['empresa_remitente'])) {
            $textContent .= $templateData['empresa_remitente'] . "\n";
        }

        $textContent .= "\n---\n";
        $textContent .= "Este email fue enviado desde la plataforma Multibolsa Inmobiliaria\n";

        return $textContent;
    }

    /**
     * Valida la configuración de Brevo
     *
     * @return bool
     */
    public function isConfigured(): bool
    {
        return !empty($this->brevoConfig['smtp_server']) &&
            !empty($this->brevoConfig['smtp_username']) &&
            !empty($this->brevoConfig['smtp_password']) &&
            !empty($this->brevoConfig['from_email']);
    }
}
