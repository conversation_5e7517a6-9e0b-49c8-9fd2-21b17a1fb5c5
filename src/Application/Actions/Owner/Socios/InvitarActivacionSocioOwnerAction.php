<?php

namespace App\Application\Actions\Owner\Socios;

use App\Application\Actions\Owner\OwnerAction;
use App\Application\Services\EmailService;
use App\Infrastructure\Models\SIConfig;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Psr\Http\Message\ResponseInterface as Response;
use Slim\Exception\HttpBadRequestException;

class InvitarActivacionSocioOwnerAction extends OwnerAction
{
    protected function action(): Response
    {
        // Obtener datos del formulario
        $formData = $this->getFormData();

        // Validar datos requeridos
        $this->validateFormData($formData);

        // Obtener información del owner autenticado
        $ownerInfo = $this->getOwnerInfo();

        if (!$ownerInfo) {
            $this->logger->error('No se pudo obtener información del owner', [
                'contrato_id' => $this->auth->getContratoId()
            ]);
            return $this->respondWithData([
                'success' => false,
                'message' => 'Error interno: No se pudo obtener información del remitente'
            ], 500);
        }

        // Obtener servicio de email
        $emailService = $this->getDependency(EmailService::class);

        if (!$emailService || !$emailService->isConfigured()) {
            $this->logger->error('Servicio de email no configurado correctamente');
            return $this->respondWithData([
                'success' => false,
                'message' => 'Error interno: Servicio de email no disponible'
            ], 500);
        }

        // Preparar datos para el template
        $templateData = $this->prepareTemplateData($formData, $ownerInfo);

        // Enviar email
        $emailSent = $emailService->enviarInvitacionSocio(
            $formData['email'],
            $formData['nombre'],
            $templateData
        );

        if ($emailSent) {
            $this->logger->info('Invitación de socio enviada exitosamente', [
                'from_contrato' => $this->auth->getContratoId(),
                'from_name' => $ownerInfo['nombre'],
                'to_email' => $formData['email'],
                'to_name' => $formData['nombre']
            ]);

            return $this->respondWithData([
                'success' => true,
                'message' => 'Invitación enviada exitosamente'
            ]);
        } else {
            $this->logger->error('Error al enviar invitación de socio', [
                'from_contrato' => $this->auth->getContratoId(),
                'to_email' => $formData['email']
            ]);

            return $this->respondWithData([
                'success' => false,
                'message' => 'Error al enviar la invitación. Por favor, inténtalo de nuevo.'
            ], 500);
        }
    }

    /**
     * Valida los datos del formulario
     */
    private function validateFormData(array $formData): void
    {
        $requiredFields = ['email', 'nombre', 'telefono', 'mensaje'];

        foreach ($requiredFields as $field) {
            if (empty($formData[$field])) {
                throw new HttpBadRequestException(
                    $this->request,
                    "El campo '$field' es requerido"
                );
            }
        }

        // Validar formato de email
        if (!filter_var($formData['email'], FILTER_VALIDATE_EMAIL)) {
            throw new HttpBadRequestException(
                $this->request,
                'El formato del email no es válido'
            );
        }
    }

    /**
     * Obtiene información del owner autenticado
     */
    private function getOwnerInfo(): ?array
    {
        try {
            $ownerData = SIConfig::join('publiweb.contratos as c', 'c.numero', '=', 'config.contrato')
                ->join('publiweb.clientes as u', 'u.usuario', '=', 'c.usuario')
                ->where('config.contrato', $this->auth->getContratoId())
                ->where('u.activo', 'Si')
                ->where('c.status', 1)
                ->select(
                    'u.name as nombre',
                    'u.empresa',
                    'u.email',
                    'u.telefono',
                    'u.ciudad',
                    'u.estado'
                )
                ->first();

            if (!$ownerData) {
                return null;
            }

            return [
                'nombre' => $ownerData->nombre,
                'empresa' => $ownerData->empresa ?: 'Multibolsa Inmobiliaria',
                'email' => $ownerData->email,
                'telefono' => $ownerData->telefono,
                'ubicacion' => trim($ownerData->ciudad . ', ' . $ownerData->estado, ', ')
            ];

        } catch (ModelNotFoundException $e) {
            $this->logger->error('Owner no encontrado', [
                'contrato_id' => $this->auth->getContratoId(),
                'error' => $e->getMessage()
            ]);
            return null;
        } catch (\Exception $e) {
            $this->logger->error('Error al obtener información del owner', [
                'contrato_id' => $this->auth->getContratoId(),
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * Prepara los datos para el template del email
     */
    private function prepareTemplateData(array $formData, array $ownerInfo): array
    {
        return [
            // Datos del destinatario
            'nombre' => $formData['nombre'],
            'telefono_destinatario' => $formData['telefono'],

            // Mensaje personalizado
            'mensaje' => nl2br(htmlspecialchars($formData['mensaje'])),

            // Datos del remitente (owner)
            'nombre_remitente' => $ownerInfo['nombre'],
            'empresa_remitente' => $ownerInfo['empresa'],
            'email_remitente' => $ownerInfo['email'],
            'telefono' => $ownerInfo['telefono'],

            // Enlaces y configuración
            'enlace_aceptacion' => $this->generateAcceptanceLink(),

            // Información adicional
            'fecha_envio' => date('d/m/Y H:i'),
            'ubicacion_remitente' => $ownerInfo['ubicacion']
        ];
    }

    /**
     * Genera el enlace de aceptación de la invitación
     */
    private function generateAcceptanceLink(): string
    {
        // Por ahora retornamos un enlace genérico
        // En el futuro se puede implementar un sistema de tokens únicos
        $baseUrl = getenv('APP_DOMAIN') ?: 'https://multibolsa.com';
        return $baseUrl . '/socios/aceptar-invitacion';
    }
}