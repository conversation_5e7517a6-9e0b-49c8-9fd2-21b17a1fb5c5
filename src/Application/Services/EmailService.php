<?php

declare(strict_types=1);

namespace App\Application\Services;

use App\Application\Settings\SettingsInterface;
use App\Infrastructure\Models\SIInvitation;
use Carbon\Carbon;
use Mustache_Engine;
use PHPMailer\PHPMailer\Exception;
use PHPMailer\PHPMailer\PHPMailer;
use Psr\Log\LoggerInterface;

class EmailService
{
    private array $brevoConfig;
    private LoggerInterface $logger;
    private Mustache_Engine $mustache;

    public function __construct(
        SettingsInterface $settings,
        LoggerInterface $logger
    ) {
        $this->brevoConfig = $settings->get('brevo');
        $this->logger = $logger;
        // Configurar Mustache sin loader (cargaremos templates manualmente)
        $this->mustache = new Mustache_Engine();
    }

    /**
     * Envía un email de invitación a socio usando Brevo SMTP y registra en BD
     *
     * @param int $contratoId ID del contrato que envía la invitación
     * @param string $toEmail Email del destinatario
     * @param string $toName Nombre del destinatario
     * @param array $templateData Datos para el template
     * @return array Resultado con success, message y datos de la invitación
     */
    public function enviarInvitacionSocio(int $contratoId, string $toEmail, string $toName, array $templateData): array
    {
        try {
            // Verificar si ya existe una invitación pendiente para este email
            $existingInvitation = SIInvitation::findPendingByEmail($toEmail);
            if ($existingInvitation) {
                return [
                    'success' => false,
                    'message' => 'Ya existe una invitación pendiente para este email',
                    'invitation_id' => $existingInvitation->id
                ];
            }

            $this->logger->debug('Attempting to render template', [
                'template' => 'invitacion-socio.html',
                'to_email' => $toEmail,
                'contrato_id' => $contratoId
            ]);

            // Cargar template directamente
            $templatesPath = dirname(__DIR__, 3) . '/templates';
            $templateFile = $templatesPath . '/invitacion-socio.html';

            $this->logger->debug('Template file path', [
                'template_file' => $templateFile,
                'file_exists' => file_exists($templateFile)
            ]);

            if (!file_exists($templateFile)) {
                throw new \Exception("Template file not found: $templateFile");
            }

            $templateContent = file_get_contents($templateFile);
            if ($templateContent === false) {
                throw new \Exception("Could not read template file: $templateFile");
            }

            // Crear registro de invitación en la base de datos
            $token = SIInvitation::generateToken();
            $invitation = new SIInvitation([
                'contrato_id' => $contratoId,
                'email' => $toEmail,
                'status' => SIInvitation::STATUS_PENDING,
                'token' => $token
            ]);

            // Agregar datos iniciales a la bitácora
            $invitation->addToBitacora([
                'action' => 'created',
                'to_name' => $toName,
                'from_contrato' => $contratoId,
                'template_data' => $templateData,
                'timestamp' => Carbon::now()->toISOString()
            ]);

            $invitation->save();

            // Agregar token al template data para el enlace de aceptación
            $templateData['token'] = $token;
            $templateData['enlace_aceptacion'] = $this->generateAcceptanceLink($token);

            // Procesar el template con Mustache
            $htmlContent = $this->mustache->render($templateContent, $templateData);

            $this->logger->debug('Template rendered successfully', [
                'content_length' => strlen($htmlContent),
                'invitation_id' => $invitation->id,
                'token' => $token
            ]);

            // Crear instancia de PHPMailer
            $mail = new PHPMailer(true);

            // Configuración del servidor SMTP
            $mail->isSMTP();
            $mail->Host = $this->brevoConfig['smtp_server'];
            $mail->SMTPAuth = true;
            $mail->Username = $this->brevoConfig['smtp_username'];
            $mail->Password = $this->brevoConfig['smtp_password'];
            $mail->SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS;
            $mail->Port = $this->brevoConfig['smtp_port'];
            $mail->CharSet = 'UTF-8';

            // Configuración del email
            $mail->setFrom(
                $this->brevoConfig['from_email'],
                $this->brevoConfig['from_name']
            );

            // Reply-To al email del remitente original
            if (!empty($templateData['email_remitente'])) {
                $mail->addReplyTo(
                    $templateData['email_remitente'],
                    $templateData['nombre_remitente'] ?? ''
                );
            }

            $mail->addAddress($toEmail, $toName);

            // Enviar copia separada al remitente con template de confirmación
            $copySentToSender = false;
            if (!empty($templateData['email_remitente']) && $templateData['email_remitente'] !== $toEmail) {
                $copySentToSender = $this->sendConfirmationCopyToSender(
                    $templateData['email_remitente'],
                    $templateData['nombre_remitente'] ?? '',
                    $templateData,
                    $invitation->id,
                    $token,
                    $toEmail,
                    $toName
                );
            }

            // Contenido del email
            $mail->isHTML(true);
            $mail->Subject = 'Invitación a Red de Socios Inmobiliarios - ' . ($templateData['nombre_remitente'] ?? 'Multibolsa');
            $mail->Body = $htmlContent;
            $mail->AltBody = $this->generateTextContent($templateData);

            $this->logger->debug('Sending email via Brevo SMTP', [
                'to_email' => $toEmail,
                'from_email' => $this->brevoConfig['from_email'],
                'confirmation_copy_to' => $templateData['email_remitente'] ?? null,
                'smtp_server' => $this->brevoConfig['smtp_server'],
                'subject' => $mail->Subject
            ]);

            // Enviar el email
            $result = $mail->send();

            if ($result) {
                // Actualizar bitácora con envío exitoso
                $bitacoraData = [
                    'action' => 'email_sent',
                    'smtp_server' => $this->brevoConfig['smtp_server'],
                    'to_email' => $toEmail,
                    'timestamp' => Carbon::now()->toISOString()
                ];

                // Agregar información de copia si se envío
                if ($copySentToSender) {
                    $bitacoraData['confirmation_copy_sent_to'] = $templateData['email_remitente'];
                }

                $invitation->addToBitacora($bitacoraData);
                $invitation->save();

                $logData = [
                    'to_email' => $toEmail,
                    'to_name' => $toName,
                    'smtp_server' => $this->brevoConfig['smtp_server'],
                    'invitation_id' => $invitation->id,
                    'token' => $token
                ];

                // Agregar información de copia de confirmación si se envío
                if ($copySentToSender) {
                    $logData['confirmation_copy_sent_to'] = $templateData['email_remitente'];
                    $logData['confirmation_copy_name'] = $templateData['nombre_remitente'] ?? '';
                }

                $this->logger->info('Email de invitación enviado exitosamente via SMTP', $logData);

                return [
                    'success' => true,
                    'message' => 'Invitación enviada exitosamente',
                    'invitation_id' => $invitation->id,
                    'token' => $token,
                    'email' => $toEmail
                ];
            } else {
                // Actualizar bitácora con error de envío
                $invitation->addToBitacora([
                    'action' => 'email_failed',
                    'error' => $mail->ErrorInfo,
                    'timestamp' => Carbon::now()->toISOString()
                ]);
                $invitation->save();

                $this->logger->error('Error al enviar email via SMTP', [
                    'to_email' => $toEmail,
                    'error_info' => $mail->ErrorInfo,
                    'invitation_id' => $invitation->id
                ]);

                return [
                    'success' => false,
                    'message' => 'Error al enviar el email: ' . $mail->ErrorInfo,
                    'invitation_id' => $invitation->id
                ];
            }
        } catch (Exception $e) {
            $this->logger->error('PHPMailer Exception al enviar email', [
                'error' => $e->getMessage(),
                'to_email' => $toEmail,
                'to_name' => $toName
            ]);
            return [
                'success' => false,
                'message' => 'Error de PHPMailer: ' . $e->getMessage()
            ];
        } catch (\Exception $e) {
            $this->logger->error('Error general al enviar email de invitación', [
                'error' => $e->getMessage(),
                'to_email' => $toEmail,
                'to_name' => $toName
            ]);
            return [
                'success' => false,
                'message' => 'Error general: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Genera contenido de texto plano para el email
     *
     * @param array $templateData
     * @return string
     */
    private function generateTextContent(array $templateData): string
    {
        $textContent = "Hola {$templateData['nombre']},\n\n";
        $textContent .= strip_tags($templateData['mensaje']) . "\n\n";
        $textContent .= "Beneficios de unirte a nuestra red:\n";
        $textContent .= "• Acceso a oportunidades exclusivas de negocio\n";
        $textContent .= "• Expansión de tu cartera de clientes y propiedades\n";
        $textContent .= "• Creación de tu propia Multibolsa Inmobiliaria\n";
        $textContent .= "• Conexión con otros profesionales del sector\n";
        $textContent .= "• Herramientas profesionales para potenciar tu negocio\n\n";

        if (isset($templateData['enlace_aceptacion'])) {
            $textContent .= "Para aceptar la invitación, visita: {$templateData['enlace_aceptacion']}\n\n";
        }

        $textContent .= "Información de contacto:\n";
        $textContent .= "Teléfono: {$templateData['telefono']}\n";
        $textContent .= "Email: {$templateData['email_remitente']}\n\n";
        $textContent .= "¡Espero que podamos colaborar pronto!\n\n";
        $textContent .= "Saludos cordiales,\n";
        $textContent .= $templateData['nombre_remitente'] . "\n";

        if (isset($templateData['empresa_remitente'])) {
            $textContent .= $templateData['empresa_remitente'] . "\n";
        }

        $textContent .= "\n---\n";
        $textContent .= "Este email fue enviado desde la plataforma Multibolsa Inmobiliaria\n";

        return $textContent;
    }

    /**
     * Valida la configuración de Brevo
     *
     * @return bool
     */
    public function isConfigured(): bool
    {
        return !empty($this->brevoConfig['smtp_server']) &&
            !empty($this->brevoConfig['smtp_username']) &&
            !empty($this->brevoConfig['smtp_password']) &&
            !empty($this->brevoConfig['from_email']);
    }

    /**
     * Genera el enlace de aceptación con token
     */
    private function generateAcceptanceLink(string $token): string
    {
        $baseUrl = getenv('APP_DOMAIN') ?: 'https://mulbin.com';
        return $baseUrl . '/register/' . $token;
    }

    /**
     * Envía copia de confirmación al remitente
     */
    private function sendConfirmationCopyToSender(
        string $senderEmail,
        string $senderName,
        array $templateData,
        int $invitationId,
        string $token,
        string $destinatarioEmail,
        string $destinatarioName
    ): bool {
        try {
            // Cargar template de confirmación
            $templatesPath = dirname(__DIR__, 3) . '/templates';
            $templateFile = $templatesPath . '/copia-invitacion-socio.html';

            if (!file_exists($templateFile)) {
                $this->logger->warning('Template de confirmación no encontrado', [
                    'template_file' => $templateFile
                ]);
                return false;
            }

            $templateContent = file_get_contents($templateFile);
            if ($templateContent === false) {
                $this->logger->error('No se pudo leer template de confirmación', [
                    'template_file' => $templateFile
                ]);
                return false;
            }

            // Preparar datos para el template de confirmación
            $confirmationData = $templateData;
            $confirmationData['invitation_id'] = $invitationId;
            $confirmationData['token'] = $token;
            // Datos del destinatario para la confirmación
            $confirmationData['email'] = $destinatarioEmail;
            $confirmationData['nombre'] = $destinatarioName;
            // Agregar enlace de aceptación para que el remitente pueda compartirlo
            $confirmationData['enlace_aceptacion'] = $this->generateAcceptanceLink($token);

            // Procesar el template
            $htmlContent = $this->mustache->render($templateContent, $confirmationData);

            // Crear nueva instancia de PHPMailer para la copia
            $copyMail = new PHPMailer(true);

            // Configuración del servidor SMTP
            $copyMail->isSMTP();
            $copyMail->Host = $this->brevoConfig['smtp_server'];
            $copyMail->SMTPAuth = true;
            $copyMail->Username = $this->brevoConfig['smtp_username'];
            $copyMail->Password = $this->brevoConfig['smtp_password'];
            $copyMail->SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS;
            $copyMail->Port = $this->brevoConfig['smtp_port'];
            $copyMail->CharSet = 'UTF-8';

            // Configuración del email de confirmación
            $copyMail->setFrom(
                $this->brevoConfig['from_email'],
                $this->brevoConfig['from_name']
            );

            $copyMail->addAddress($senderEmail, $senderName);

            // Contenido del email de confirmación
            $copyMail->isHTML(true);
            $copyMail->Subject = 'Confirmación: Invitación enviada a ' . $templateData['nombre'];
            $copyMail->Body = $htmlContent;
            $copyMail->AltBody = $this->generateConfirmationTextContent($confirmationData, $invitationId, $token);

            // Enviar email de confirmación
            $result = $copyMail->send();

            if ($result) {
                $this->logger->debug('Copia de confirmación enviada exitosamente', [
                    'sender_email' => $senderEmail,
                    'invitation_id' => $invitationId
                ]);
                return true;
            } else {
                $this->logger->error('Error al enviar copia de confirmación', [
                    'sender_email' => $senderEmail,
                    'error' => $copyMail->ErrorInfo
                ]);
                return false;
            }
        } catch (Exception $e) {
            $this->logger->error('Excepción al enviar copia de confirmación', [
                'sender_email' => $senderEmail,
                'error' => $e->getMessage()
            ]);
            return false;
        } catch (\Exception $e) {
            $this->logger->error('Error general al enviar copia de confirmación', [
                'sender_email' => $senderEmail,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Genera contenido de texto plano para el email de confirmación
     */
    private function generateConfirmationTextContent(array $templateData, int $invitationId, string $token): string
    {
        $textContent = "CONFIRMACIÓN: Invitación Enviada Exitosamente\n\n";
        $textContent .= "Tu invitación ha sido enviada a {$templateData['nombre']} ({$templateData['email']}).\n\n";
        $textContent .= "Detalles de la invitación:\n";
        $textContent .= "- Destinatario: {$templateData['nombre']}\n";
        $textContent .= "- Email: {$templateData['email']}\n";
        $textContent .= "- Teléfono: {$templateData['telefono_destinatario']}\n";
        $textContent .= "- Fecha de envío: {$templateData['fecha_envio']}\n\n";
        $textContent .= "Próximos pasos:\n";
        $textContent .= "1. El destinatario recibirá el email con tu invitación\n";
        $textContent .= "2. Podrá aceptar la invitación haciendo clic en el enlace\n";
        $textContent .= "3. Se creará automáticamente la relación de socios directos\n";
        $textContent .= "4. Recibirás una notificación cuando acepte la invitación\n\n";

        // Agregar enlace de aceptación
        if (isset($templateData['enlace_aceptacion'])) {
            $textContent .= "ENLACE DE ACEPTACIÓN (para compartir si es necesario):\n";
            $textContent .= "{$templateData['enlace_aceptacion']}\n\n";
        }

        $textContent .= "Token de seguimiento: $token\n";
        $textContent .= "ID de invitación: $invitationId\n\n";
        $textContent .= "---\n";
        $textContent .= "Esta es una confirmación automática de Multibolsa Inmobiliaria\n";

        return $textContent;
    }
}
